import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { ReportIncident, Action } from '../models';
import { ReportIncidentRepository, ActionRepository, UserRepository, UserLocationRoleRepository, LocationOneRepository, LocationTwoRepository, LocationThreeRepository, LocationFourRepository, LocationFiveRepository, LocationSixRepository } from '../repositories';
import { v4 as uuidv4 } from 'uuid';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import moment from 'moment';
import { SqsService } from '../services/sqs-service.service';
import { LocationFilterService } from '../services';
import { report } from 'process';


function getPast12Months(): string[] {
  const months = [];
  for (let i = 11; i >= 0; i--) {
    const monthYear = moment().subtract(i, 'months').format('YYYY-MM');
    months.push(monthYear);
  }
  return months;
}

export class ReportIncidentController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @repository(LocationFiveRepository)
    public locationFiveRepository: LocationFiveRepository,
    @repository(LocationSixRepository)
    public locationSixRepository: LocationSixRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject('services.LocationFilterService')
    public locationFilterService: LocationFilterService,
  ) { }

  @authenticate('jwt')
  @post('/report-incidents')
  @response(200, {
    description: 'ReportIncident model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ReportIncident) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, {
            title: 'NewReportIncident',
            exclude: ['id'],
          }),
        },
      },
    })
    reportIncident: Omit<ReportIncident, 'id'>,
  ): Promise<ReportIncident> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    //generate id
    const count = await this.reportIncidentRepository.count();
    reportIncident.maskId = `INC-${moment().format('YYMMDD')}-${String(count.count + 1).padStart(4, '0')}`;
    if (user) {
      reportIncident.userId = user.id;
      reportIncident.status = 'Under Review'
      reportIncident.stage = 'Supplementary Information in progress'

      const userRoles = await this.userLocationRoleRepository.find();
      const locationOne = await this.locationOneRepository.findById(reportIncident.locationOneId)
      const locationTwo = await this.locationTwoRepository.findById(reportIncident.locationTwoId)
      const locationThree = await this.locationThreeRepository.findById(reportIncident.locationThreeId)
      const locationFour = await this.locationFourRepository.findById(reportIncident.locationFourId)
      // const locationFive = await this.locationFiveRepository.findById(reportIncident.locationFiveId)
      // const locationSix = await this.locationSixRepository.findById(reportIncident.locationSixId)
      // Use .filter() to find roles where the roles array includes locationOneId

      const filteredRoles = userRoles.filter(role =>
        role.roles?.includes('64eeb4159cd11998dbbc0d48')
      );


      // Extract unique user IDs from the filtered roles
      const userIds = Array.from(new Set(filteredRoles.map(role => role.userId)));


      if (userIds.length === 0) {
        console.log(`No users found with roles for location`);
      }


      // Fetch user details for the matched user IDs
      const users = await this.userRepository.find({
        where: { id: { inq: userIds } },

      });



      const mailSubject = `Notification of Preliminary Incident Report by ${user.firstName} - ${reportIncident.maskId}`;
      const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Incident Report</title>
      </head>
      <body>
          <p><strong>Incident Title: </strong> ${reportIncident.title}</p>
          
          <p>The above incident has been reported by ${user.firstName}. Please note this is only a preliminary notification of the incident and this has not yet been reviewed by a designated incident reviewer. </p>

          <p><strong>Incident is reported at Location: </strong> ${locationOne.name} &gt; ${locationTwo.name}  &gt; ${locationThree.name}  &gt; ${locationFour.name} </p>
        
          <p><strong>Description of the Incident: </strong> ${reportIncident.description}</p>
          <p><strong>Impact Classification: </strong> ${reportIncident.actualImpact}</p>
      </body>
      </html>`;

      for (const u of users) {
        if (u) {
          try {
            // Assuming user has email or required details in `user`
            await this.sqsService.sendMessage(u, mailSubject, mailBody);
          } catch (error) {
            console.error(`Failed to send email to user: ${user.email}. Error: ${error.message}`);
            // Optionally handle retry logic here
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again.`);
        }
      }

      const rmailSubject = `Action Required: Review Preliminary Incident Report by ${user.firstName} - ${reportIncident.maskId}`;
      const rmailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Incident Report</title>
      </head>
      <body>
          <p><strong>Incident Title: </strong> ${reportIncident.title}</p>
          
          <p>The above incident has been reported by ${user.firstName}. This incident report contains preliminary information provided by the incident reporter. Please review the details and add any necessary supplementary information. The incident reporter may continue to update the report, but as the incident reviewer, it is your responsibility to finalize and submit the completed report. </p>

          <p><strong>Incident is reported at Location: </strong> ${locationOne.name} &gt; ${locationTwo.name}  &gt; ${locationThree.name}  &gt; ${locationFour.name} </p>
        
          <p><strong>Description of the Incident: </strong> ${reportIncident.description}</p>
          <p><strong>Impact Classification: </strong> ${reportIncident.actualImpact}</p>
      </body>
      </html>`;
      const reportIncidentData = await this.reportIncidentRepository.create(reportIncident);

      if (reportIncident.reviewerId) {

        const modifiedActions = {
          application: "INCIDENT",
          actionType: "review_incident",
          description: `${uuidv4()}`,
          actionToBeTaken: "Review Incident",
          dueDate: "",
          status: 'open',
          createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
          objectId: reportIncidentData.id,
          submittedById: user?.id,
          assignedToId: reportIncident.reviewerId

        }

        await this.actionRepository.create(modifiedActions)
        const reviewer = await this.userRepository.findById(reportIncident.reviewerId)
        try {
          // Assuming user has email or required details in `user`
          await this.sqsService.sendMessage(reviewer, rmailSubject, rmailBody);
        } catch (error) {
          console.error(`Failed to send email to user: ${user.email}. Error: ${error.message}`);
          // Optionally handle retry logic here
        }
      }

      const ehsRoles = await this.locationFilterService.getMatchingUserLocationRolesByRoleAndLocations('64c4a4c19b2ecb108a42576b', reportIncident.locationOneId, reportIncident.locationTwoId, reportIncident.locationThreeId, reportIncident.locationFourId);

      const ehsIds = Array.from(new Set(ehsRoles.map(role => role.userId)));

      if (ehsIds.length === 0) {
        console.log(`No users found with EHS roles for location`);
      }


      const ehsUsers = await this.userRepository.find({
        where: { id: { inq: ehsIds } },
      });

      for (const u of ehsUsers) {
        if (u) {
          try {
            // Assuming user has email or required details in `user`
            await this.sqsService.sendMessage(u, mailSubject, mailBody);
          } catch (error) {
            console.error(`Failed to send email to user: ${user.email}. Error: ${error.message}`);
            // Optionally handle retry logic here
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again.`);
        }
      }



      return reportIncidentData;
    } else {
      throw new HttpErrors.NotFound('User not Found')
    }

  }






  @get('/report-incidents/count')
  @response(200, {
    description: 'ReportIncident model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ReportIncident) where?: Where<ReportIncident>,
  ): Promise<Count> {
    return this.reportIncidentRepository.count(where);
  }

  @get('/report-incidents')
  @response(200, {
    description: 'Array of ReportIncident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    return this.reportIncidentRepository.find({ where: { userId: user?.id } });
  }

  @get('/all-report-incidents')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findByLocation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };

      const incidentReports = await this.reportIncidentRepository.find({
        ...filter,
        where: {
          status: { neq: 'Reported' },
          ...whereClause
        }
      });

      const modifiedIncidentReports = await Promise.all(
        incidentReports.map(async (data) => {
          const totalActions = await this.actionRepository.find({ where: { objectId: data.id } });
          const completedActions = totalActions.filter(i => i.status === 'completed');

          // Create an instance of ReportIncident with the desired properties
          const modifiedReport = new ReportIncident({
            ...data,
            incidentData: {
              ...data.incidentData,
              totalActions: totalActions,
              completedActions: completedActions
            }
          });

          return modifiedReport;
        })
      );

      return modifiedIncidentReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }


  @get('/report-incidents-review')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findByReview(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);

      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };
      const incidentReports = await this.reportIncidentRepository.find({
        ...filter,
        where: whereClause
      });
      return incidentReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }


  @get('/get-report-incidents-reviewer')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findByReviewer(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);

      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };
      const incidentReports = await this.reportIncidentRepository.find({
        ...filter,
        where: whereClause
      });
      return incidentReports.filter(i => i.reviewerId === user.id);

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }


  @get('/get-report-incidents-incident-owner')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findByIncidentOwner(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);

      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };
      const incidentReports = await this.reportIncidentRepository.find({
        ...filter,
        where: whereClause
      });
      return incidentReports.filter(i => i.incidentOwnerId === user.id);

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }


  @get('/get-report-incidents-reporter')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findByReporter(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);

      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };
      const incidentReports = await this.reportIncidentRepository.find({
        ...filter,
        where: whereClause
      });
      return incidentReports.filter(i => i.userId === user.id);

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @get('/report-incidents-investigate')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findByInvestigate(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };

      const incidentReports = await this.reportIncidentRepository.find({
        ...filter,
        where: {
          status: { inq: ['Under Investigation', 'Investigation Completed', 'Reinvestigate'] },
          ...whereClause
        }
      });
      return incidentReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @get('/report-incidents-lead-investigate')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findByLeadInvestigate(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {

      const incidentReports = await this.reportIncidentRepository.find({
        ...filter,
        where: {
          status: { inq: ['Under Investigation', 'Investigation Completed', 'Reinvestigate'] },
          investigatorId: user.id,

        }
      });
      return incidentReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @get('/report-incidents-trigger')
  @response(200, {
    description: 'Array of ReportIncident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findTrigger(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };

      return this.reportIncidentRepository.find({
        where: {
          investigationStatus: true,
          ...whereClause
        }
      });
    } else {
      throw new HttpErrors.NotFound('user not found')
    }
  }

  @patch('/report-incidents')
  @response(200, {
    description: 'ReportIncident PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
    @param.where(ReportIncident) where?: Where<ReportIncident>,
  ): Promise<Count> {
    return this.reportIncidentRepository.updateAll(reportIncident, where);
  }

  @get('/report-incidents/{id}')
  @response(200, {
    description: 'ReportIncident model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ReportIncident, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ReportIncident, { exclude: 'where' }) filter?: FilterExcludingWhere<ReportIncident>
  ): Promise<ReportIncident> {
    return this.reportIncidentRepository.findById(id, filter);
  }

  @patch('/report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @patch('/report-incidents-additional-files/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateAdditionalFilesById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    const reportData = await this.reportIncidentRepository.findById(id);
    if (reportData) {
      reportIncident.additionalDocuments = [...(reportData.additionalDocuments ?? []), ...(reportIncident.additionalDocuments ?? [])];
    }
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @authenticate('jwt')
  @patch('/report-incidents-review/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateReviewById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Reviewed'

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (!user) {
      throw new Error('Unauthorized')
    }


    if (reportIncident.riskControl) {
      const controlMeasures = reportIncident.riskControl.controlMeasures || [];
      const riskAssessment = reportIncident.riskControl.riskAssessment || [];
      if (reportIncident.isControlMeasure && controlMeasures && controlMeasures.length > 0) {
        const modifiedActions = controlMeasures.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_control",
            description: `${uuidv4()}`,
            actionToBeTaken: i.controlMeasures,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)
      }

      if (reportIncident.isRiskAssessment && riskAssessment && riskAssessment.length > 0) {
        const modifiedActions = riskAssessment.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_ra",
            description: `${uuidv4()}`,
            actionToBeTaken: i.name,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)
      }
    }
    // actionItem = [...actionItem,
    //   {
    //     application: "AIR",
    //     actionType: "air_surveyor",

    //     description: airReportData.description,
    //     dueDate: '',

    //     status: "open",
    //     createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
    //     objectId: airReportData.id,
    //     submittedById: user?.id,
    //     assignedToId: airReportData.reviewerId
    //   }]
    if (["Level 1", "Level 2", "Near Miss"].includes(reportIncident?.actualImpact || '')) {
      const incidentData = await this.reportIncidentRepository.findById(id, {
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
        ]
      })
      const incidentOwner = await this.userRepository.findById(incidentData.incidentOwnerId)
      const mailSubject = `Notification of Updated Incident Report - ${incidentData.maskId}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Incident Report Email</title>
        </head>
        <body>
            
          <p>Incident Title: ${incidentData.title}</p>

          <p><strong>Incident is reported at Location: </strong> 
    ${incidentData.locationOne?.name ?? ''} 
    ${incidentData.locationTwo?.name ? ` > ${incidentData.locationTwo.name}` : ''} 
    ${incidentData.locationThree?.name ? ` > ${incidentData.locationThree.name}` : ''} 
    ${incidentData.locationFour?.name ? ` > ${incidentData.locationFour.name}` : ''} 
    ${incidentData.locationFive?.name ? ` > ${incidentData.locationFive.name}` : ''} 
    ${incidentData.locationSix?.name ? ` > ${incidentData.locationSix.name}` : ''}
</p>

         <p>Details of the above incident have been updated based on a review by a designated incident reviewer. You can <a href="https://stt-gdc.acuizen.com" target="_blank">log in to the incident portal</a> on the web page to view the updated details.</p>

          

          <p>Description of the Incident: ${incidentData.description}</p>

          <p>Impact Classification: ${incidentData.actualImpact}</p>
        </body>
        </html>`;


      if (incidentOwner) { await this.sqsService.sendMessage(incidentOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    if (["Level 3", "Level 4", "Level 5"].includes(reportIncident?.actualImpact || '')) {
      const incidentData = await this.reportIncidentRepository.findById(id, {
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
        ]
      })
      const incidentOwner = await this.userRepository.findById(incidentData.incidentOwnerId)
      const mailSubject = `Notification of Updated Incident Report - ${incidentData.maskId}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Incident Report Email</title>
        </head>
        <body>
            
          <p>Incident Title: ${incidentData.title}</p>

          <p><strong>Incident is reported at Location: </strong> 
    ${incidentData.locationOne?.name ?? ''} 
    ${incidentData.locationTwo?.name ? ` > ${incidentData.locationTwo.name}` : ''} 
    ${incidentData.locationThree?.name ? ` > ${incidentData.locationThree.name}` : ''} 
    ${incidentData.locationFour?.name ? ` > ${incidentData.locationFour.name}` : ''} 
    ${incidentData.locationFive?.name ? ` > ${incidentData.locationFive.name}` : ''} 
    ${incidentData.locationSix?.name ? ` > ${incidentData.locationSix.name}` : ''}
</p>

         <p>Details of the above incident have been updated based on a review by a designated incident reviewer. You can <a href="https://stt-gdc.acuizen.com" target="_blank">log in to the incident portal</a> on the web page to view the updated details. Please note that this action can be done only on the web page and not on the mobile app.</p> <p>Please trigger an investigation to proceed with addressing the incident.</p>

          
  
          <p>Description of the Incident: ${incidentData.description}</p>

          <p>Impact Classification: ${incidentData.actualImpact}</p>
        </body>
        </html>`;


      if (incidentOwner) { await this.sqsService.sendMessage(incidentOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }
    reportIncident.reviewerId = user.id;
    // await this.actionRepository.updateAll({ status: 'submitted' }, { objectId: id });
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('v2/report-incidents-review-pre-submit/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updatePreReviewById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Closed'
    reportIncident.stage = 'Preliminary analysis completed'

    await this.actionRepository.updateAll({ status: 'submitted' }, { objectId: id });

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (!user) {
      throw new Error('Unauthorized')
    }


    if (reportIncident.riskControl) {
      const controlMeasures = reportIncident.riskControl.controlMeasures || [];
      const riskAssessment = reportIncident.riskControl.riskAssessment || [];
      if (reportIncident.isControlMeasure && controlMeasures && controlMeasures.length > 0) {
        const modifiedActions = controlMeasures.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_control",
            description: `${uuidv4()}`,
            actionToBeTaken: i.controlMeasures,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)
      }

      if (reportIncident.isRiskAssessment && riskAssessment && riskAssessment.length > 0) {
        const modifiedActions = riskAssessment.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_ra",
            description: `${uuidv4()}`,
            actionToBeTaken: i.name,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)
      }
    }
    // actionItem = [...actionItem,
    //   {
    //     application: "AIR",
    //     actionType: "air_surveyor",

    //     description: airReportData.description,
    //     dueDate: '',

    //     status: "open",
    //     createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
    //     objectId: airReportData.id,
    //     submittedById: user?.id,
    //     assignedToId: airReportData.reviewerId
    //   }]
    if (["Level 1", "Level 2", "Near Miss"].includes(reportIncident?.actualImpact || '')) {
      const incidentData = await this.reportIncidentRepository.findById(id, {
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
        ]
      })
      const incidentOwner = await this.userRepository.findById(incidentData.incidentOwnerId)
      const mailSubject = `Notification of Updated Incident Report - ${incidentData.maskId}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Incident Report Email</title>
        </head>
        <body>
            
          <p>Incident Title: ${incidentData.title}</p>

          <p><strong>Incident is reported at Location: </strong> 
    ${incidentData.locationOne?.name ?? ''} 
    ${incidentData.locationTwo?.name ? ` > ${incidentData.locationTwo.name}` : ''} 
    ${incidentData.locationThree?.name ? ` > ${incidentData.locationThree.name}` : ''} 
    ${incidentData.locationFour?.name ? ` > ${incidentData.locationFour.name}` : ''} 
    ${incidentData.locationFive?.name ? ` > ${incidentData.locationFive.name}` : ''} 
    ${incidentData.locationSix?.name ? ` > ${incidentData.locationSix.name}` : ''}
</p>

         <p>Details of the above incident have been updated based on a review by a designated incident reviewer. You can <a href="https://stt-gdc.acuizen.com" target="_blank">log in to the incident portal</a> on the web page to view the updated details.</p>

          

          <p>Description of the Incident: ${incidentData.description}</p>

          <p>Impact Classification: ${incidentData.actualImpact}</p>
        </body>
        </html>`;


      if (incidentOwner) { await this.sqsService.sendMessage(incidentOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    if (["Level 3", "Level 4", "Level 5"].includes(reportIncident?.actualImpact || '')) {
      const incidentData = await this.reportIncidentRepository.findById(id, {
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
        ]
      })
      const incidentOwner = await this.userRepository.findById(incidentData.incidentOwnerId)
      const mailSubject = `Notification of Updated Incident Report - ${incidentData.maskId}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Incident Report Email</title>
        </head>
        <body>
            
          <p>Incident Title: ${incidentData.title}</p>

          <p><strong>Incident is reported at Location: </strong> 
    ${incidentData.locationOne?.name ?? ''} 
    ${incidentData.locationTwo?.name ? ` > ${incidentData.locationTwo.name}` : ''} 
    ${incidentData.locationThree?.name ? ` > ${incidentData.locationThree.name}` : ''} 
    ${incidentData.locationFour?.name ? ` > ${incidentData.locationFour.name}` : ''} 
    ${incidentData.locationFive?.name ? ` > ${incidentData.locationFive.name}` : ''} 
    ${incidentData.locationSix?.name ? ` > ${incidentData.locationSix.name}` : ''}
</p>

         <p>Details of the above incident have been updated based on a review by a designated incident reviewer. You can <a href="https://stt-gdc.acuizen.com" target="_blank">log in to the incident portal</a> on the web page to view the updated details. Please note that this action can be done only on the web page and not on the mobile app.</p> <p>Please trigger an investigation to proceed with addressing the incident.</p>

          
  
          <p>Description of the Incident: ${incidentData.description}</p>

          <p>Impact Classification: ${incidentData.actualImpact}</p>
        </body>
        </html>`;


      if (incidentOwner) { await this.sqsService.sendMessage(incidentOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    // await this.actionRepository.updateAll({ status: 'submitted' }, { objectId: id });
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('v2/report-incidents-review/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateReviewV2ById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.stage = 'Preliminary analysis is in progress'
    reportIncident.status = 'Reported'
    await this.actionRepository.updateAll({ status: 'submitted' }, { objectId: id });
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const incidentDataNew = await this.reportIncidentRepository.findById(id)
    if (!user) {
      throw new Error('Unauthorized')
    }


    const actionItem = {
      application: "INCIDENT",
      actionType: "preliminary_incident_report",

      description: incidentDataNew.description,
      dueDate: '',

      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: id,
      submittedById: user?.id,
      assignedToId: incidentDataNew.reviewerId
    }
    await this.actionRepository.create(actionItem)

    if (["Level 1", "Level 2", "Near Miss"].includes(reportIncident?.classification || '')) {
      const incidentData = await this.reportIncidentRepository.findById(id, {
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
        ]
      })
      const incidentOwner = await this.userRepository.findById(incidentData.incidentOwnerId)
      const mailSubject = `Notification of Updated Incident Report - ${incidentData.maskId}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Incident Report Email</title>
        </head>
        <body>
            
          <p>Incident Title: ${incidentData.title}</p>

          <p><strong>Incident is reported at Location: </strong> 
    ${incidentData.locationOne?.name ?? ''} 
    ${incidentData.locationTwo?.name ? ` > ${incidentData.locationTwo.name}` : ''} 
    ${incidentData.locationThree?.name ? ` > ${incidentData.locationThree.name}` : ''} 
    ${incidentData.locationFour?.name ? ` > ${incidentData.locationFour.name}` : ''} 
    ${incidentData.locationFive?.name ? ` > ${incidentData.locationFive.name}` : ''} 
    ${incidentData.locationSix?.name ? ` > ${incidentData.locationSix.name}` : ''}
</p>

         <p>Details of the above incident have been updated based on a review by a designated incident reviewer. You can <a href="https://stt-gdc.acuizen.com" target="_blank">log in to the incident portal</a> on the web page to view the updated details.</p>

          

          <p>Description of the Incident: ${incidentData.description}</p>

          <p>Impact Classification: ${incidentData.classification}</p>
        </body>
        </html>`;


      if (incidentOwner) { await this.sqsService.sendMessage(incidentOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    if (["Level 3", "Level 4", "Level 5"].includes(reportIncident?.classification || '')) {
      const incidentData = await this.reportIncidentRepository.findById(id, {
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
        ]
      })
      const incidentOwner = await this.userRepository.findById(incidentData.incidentOwnerId)
      const mailSubject = `Notification of Updated Incident Report - ${incidentData.maskId}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Incident Report Email</title>
        </head>
        <body>
            
          <p>Incident Title: ${incidentData.title}</p>

          <p><strong>Incident is reported at Location: </strong> 
    ${incidentData.locationOne?.name ?? ''} 
    ${incidentData.locationTwo?.name ? ` > ${incidentData.locationTwo.name}` : ''} 
    ${incidentData.locationThree?.name ? ` > ${incidentData.locationThree.name}` : ''} 
    ${incidentData.locationFour?.name ? ` > ${incidentData.locationFour.name}` : ''} 
    ${incidentData.locationFive?.name ? ` > ${incidentData.locationFive.name}` : ''} 
    ${incidentData.locationSix?.name ? ` > ${incidentData.locationSix.name}` : ''}
</p>

         <p>Details of the above incident have been updated based on a review by a designated incident reviewer. You can <a href="https://stt-gdc.acuizen.com" target="_blank">log in to the incident portal</a> on the web page to view the updated details. Please note that this action can be done only on the web page and not on the mobile app.</p> <p>Please trigger an investigation to proceed with addressing the incident.</p>

          
  
          <p>Description of the Incident: ${incidentData.description}</p>

          <p>Impact Classification: ${incidentData.classification}</p>
        </body>
        </html>`;


      if (incidentOwner) { await this.sqsService.sendMessage(incidentOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }
    reportIncident.reviewerId = user.id;
    // await this.actionRepository.updateAll({ status: 'submitted' }, { objectId: id });
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('v2/report-incidents-reviewer-submit/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateReviewSubmitById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.stage = 'Preliminary analysis completed'
    reportIncident.status = 'Closed'

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    // await this.actionRepository.updateAll({ status: 'submitted' }, { objectId: id });
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('/report-incidents-create-control-measures/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async createControlMeasures(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const incidentData = await this.reportIncidentRepository.findById(id)
    if (!user) {
      throw new Error('Unauthorized')
    }


    if (reportIncident.riskControl) {
      const controlMeasures = reportIncident.riskControl.controlMeasures || [];
      const riskAssessment = reportIncident.riskControl.riskAssessment || [];
      console.log(controlMeasures, ' Cont')
      if (controlMeasures && controlMeasures.length > 0) {

        console.log(controlMeasures, ' Cont 1')
        const modifiedActions = controlMeasures.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_control",
            description: `${uuidv4()}`,
            actionToBeTaken: i.controlMeasures,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        console.log(modifiedActions, ' Cont 3')
        try {
          const createdActions = await this.actionRepository.createAll(modifiedActions);
          console.log("Actions successfully created ", createdActions);
        } catch (error) {
          console.error("Error creating actions:", error);
        }

        reportIncident.riskControl.controlMeasures = [...incidentData.riskControl.controlMeasures, ...reportIncident.riskControl.controlMeasures]
      }

      if (riskAssessment && riskAssessment.length > 0) {
        const modifiedActions = riskAssessment.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_ra",
            description: `${uuidv4()}`,
            actionToBeTaken: i.name,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        try {
          await this.actionRepository.createAll(modifiedActions);
          console.log("Actions successfully created");
        } catch (error) {
          console.error("Error creating actions:", error);
        }

        reportIncident.riskControl.riskAssessment = [...incidentData.riskControl.riskAssessment, ...reportIncident.riskControl.riskAssessment]
      }
    }
    // actionItem = [...actionItem,
    //   {
    //     application: "AIR",
    //     actionType: "air_surveyor",

    //     description: airReportData.description,
    //     dueDate: '',

    //     status: "open",
    //     createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
    //     objectId: airReportData.id,
    //     submittedById: user?.id,
    //     assignedToId: airReportData.reviewerId
    //   }]


    // await this.actionRepository.updateAll({ status: 'submitted' }, { objectId: id });
    console.log("Updating report incident:", reportIncident);
    await this.reportIncidentRepository.updateById(id, reportIncident);
    console.log("Update successful");
  }


  @authenticate('jwt')
  @patch('/report-incidents-manually-create-control-measures/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async createManualControlMeasures(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const incidentData = await this.reportIncidentRepository.findById(id)
    if (!user) {
      throw new Error('Unauthorized')
    }


    if (incidentData.riskControl) {
      const controlMeasures = incidentData.riskControl.controlMeasures || [];
      const riskAssessment = incidentData.riskControl.riskAssessment || [];
      if (incidentData.isControlMeasure && controlMeasures && controlMeasures.length > 0) {
        const modifiedActions = controlMeasures.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_control",
            description: `${uuidv4()}`,
            actionToBeTaken: i.controlMeasures,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)

      }

      if (incidentData.isRiskAssessment && riskAssessment && riskAssessment.length > 0) {
        const modifiedActions = riskAssessment.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_ra",
            description: `${uuidv4()}`,
            actionToBeTaken: i.name,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)

      }
    }
    // actionItem = [...actionItem,
    //   {
    //     application: "AIR",
    //     actionType: "air_surveyor",

    //     description: airReportData.description,
    //     dueDate: '',

    //     status: "open",
    //     createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
    //     objectId: airReportData.id,
    //     submittedById: user?.id,
    //     assignedToId: airReportData.reviewerId
    //   }]


    // await this.actionRepository.updateAll({ status: 'submitted' }, { objectId: id });

  }


  @authenticate('jwt')
  @patch('/report-incidents-investigation-verify/{id}/{action_id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateInvesigationVerifyById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Investigation Completed'
    await this.actionRepository.updateById(action_id, { status: 'submitted' })
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const reportIncidentData = await this.reportIncidentRepository.findById(id)
    let actionItem = []

    if (reportIncidentData.investigationStep) {
      const controlMeasures = reportIncidentData.investigationStep?.dynamicForm?.actions || [];

      if (controlMeasures && controlMeasures.length > 0) {
        const modifiedActions = controlMeasures.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_control_post",
            description: `${uuidv4()}`,
            actionToBeTaken: i.description,
            dueDate: i.date,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: reportIncidentData.investigatorId,
            assignedToId: i.personResponsible,

          }
        })

        await this.actionRepository.createAll(modifiedActions)
      }
    }


    // actionItem = [...actionItem,
    //   {
    //     application: "AIR",
    //     actionType: "air_surveyor",

    //     description: airReportData.description,
    //     dueDate: '',

    //     status: "open",
    //     createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
    //     objectId: airReportData.id,
    //     submittedById: user?.id,
    //     assignedToId: airReportData.reviewerId
    //   }]

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('/report-incidents-investigation-reinvestigate/{id}/{action_id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateReInvesigationVerifyById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Reinvestigate'

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    await this.actionRepository.updateById(action_id, { status: 'submitted' })

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('/report-incidents-investigation/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateInvestigationById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {

    reportIncident.status = 'Investigated'
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const reportData = await this.reportIncidentRepository.findById(id)

    const actionItem = {
      application: "INCIDENT",
      actionType: "verify_investigation_actions",

      description: reportData.description,
      dueDate: '',

      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: id,
      submittedById: user?.id,
      assignedToId: reportData.incidentOwnerId
    }

    await this.actionRepository.create(actionItem)
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('/save-report-incidents-investigation/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateSaveInvestigationById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {


    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @authenticate('jwt')
  @patch('/report-incidents-investigate/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateInvestigateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Under Investigation'
    if (reportIncident.investigationStatus) {

    } else {
      reportIncident.status = 'Tracked'
    }
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });



    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @authenticate('jwt')
  @patch('/report-incidents-trigger-investigate/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateTriggerInvestigateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Under Investigation'
    
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    reportIncident.investigatorId = user?.id ?? ''

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @authenticate('jwt')
  @patch('report-incident/actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateActionsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const reportData = await this.reportIncidentRepository.findById(action.objectId)
    action.status = 'submitted'
    if (action.actionType === 'take_actions_ra' || action.actionType === 'take_actions_control' || action.actionType === 'take_actions_control_post' || action.actionType === 'retake_actions' || action.actionType === 'take_investigation_actions') {
      delete action.actionType

      await this.reportIncidentRepository.updateById(action.objectId, { evidence: action.uploads })

      const actionItem = {
        application: "INCIDENT",
        actionType: "verify_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: reportData.incidentOwnerId
      }


      if (reportData.incidentOwnerId && action.objectId) {

        const observationDetails = await this.reportIncidentRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })
        const reviewerDetails = await this.userRepository.findById(reportData.incidentOwnerId)

        const locationOneName = (observationDetails as any).locationOne?.name;
        const locationTwoName = (observationDetails as any).locationTwo?.name;
        const locationThreeName = (observationDetails as any).locationThree?.name;
        const locationFourName = (observationDetails as any).locationFour?.name;
        const locationFiveName = (observationDetails as any).locationFive?.name;
        const locationSixName = (observationDetails as any).locationSix?.name;

        const mailSubject = `Action Required: Verify Post Incident Action: ${locationFiveName}, ${locationSixName}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Incident Report Email</title>
        </head>
        <body>
            <p>Incident Title: ${observationDetails.title}</p>
          <p>Description of the Incident: ${observationDetails.description}</p>

          <p>Impact Classification: ${observationDetails.actualImpact}</p>
           
            <p><strong>Name of Assignee: </strong> ${user?.firstName}</p>
            <p><strong>Location: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
           
            <p><strong>Rectification action taken by Assignee: </strong> ${action.actionTaken}</p>
            <p><strong>Evidence:</strong></p>
            <ul>
                ${action.uploads?.map(upload => `<li><a href="${process.env.STATIC_URL}/docs/${encodeURIComponent(upload)}">${upload}</a></li>`) ?? ''}
            </ul>
        </body>
        </html>`;


        if (reviewerDetails) { await this.sqsService.sendMessage(reviewerDetails, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

      await this.actionRepository.create(actionItem)

    }

    if (action.actionType === 'approve') {

      if (action.objectId) {

        const observationDetails = await this.reportIncidentRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })



      }

    }

    if (action.actionType === 'reject') {

      const actionItem = {
        application: "INCIDENT",
        actionType: "retake_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.submittedById
      }

      await this.actionRepository.create(actionItem)
    }
    delete action.assignedToId
    await this.actionRepository.updateById(id, action);
  }

  @authenticate('jwt')
  @patch('/report-incidents-investigate-close/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateInvestigateCloseById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Tracked'

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });



    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @patch('/save-report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateByIdWithoutInvestigation(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    //incidentOwnerId



    const incidentData = await this.reportIncidentRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
      ]
    })
    if (incidentData.incidentOwnerId !== reportIncident.incidentOwnerId) {

      const incidentOwner = await this.userRepository.findById(reportIncident.incidentOwnerId)
      const mailSubject = `Notification of Updated Incident Report - ${incidentData.maskId}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Incident Report Email</title>
        </head>
        <body>
            
          <p>Incident Title: ${incidentData.title}</p>

          <p><strong>Incident is reported at Location: </strong> 
    ${incidentData.locationOne?.name ?? ''} 
    ${incidentData.locationTwo?.name ? ` > ${incidentData.locationTwo.name}` : ''} 
    ${incidentData.locationThree?.name ? ` > ${incidentData.locationThree.name}` : ''} 
    ${incidentData.locationFour?.name ? ` > ${incidentData.locationFour.name}` : ''} 
    ${incidentData.locationFive?.name ? ` > ${incidentData.locationFive.name}` : ''} 
    ${incidentData.locationSix?.name ? ` > ${incidentData.locationSix.name}` : ''}
</p>

         <p>Details of the above incident have been updated based on a review by a designated incident reviewer. You can <a href="https://stt-gdc.acuizen.com" target="_blank">log in to the incident portal</a> on the web page to view the updated details.</p>

          

          <p>Description of the Incident: ${incidentData.description}</p>

          <p>Impact Classification: ${incidentData.actualImpact}</p>
        </body>
        </html>`;


      if (incidentOwner) { await this.sqsService.sendMessage(incidentOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }



  @patch('/return-report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async returnByIdWithoutInvestigation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const incidentData = await this.reportIncidentRepository.findById(id)
    const actionItem = {
      application: "INCIDENT",
      actionType: "returned_incident",
      comments: '',
      actionTaken: '',
      actionToBeTaken: '',
      description: incidentData.description,
      dueDate: '',

      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: id,
      submittedById: user?.id,
      assignedToId: incidentData.userId
    }

    await this.actionRepository.create(actionItem)

    reportIncident.status = 'Returned'
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @patch('/resubmit-report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async resubmitByIdWithoutInvestigation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    reportIncident.status = 'Reported';
    reportIncident.userId = user?.id ?? '';
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @put('/report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() reportIncident: ReportIncident,
  ): Promise<void> {
    await this.reportIncidentRepository.replaceById(id, reportIncident);
  }

  @del('/report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.reportIncidentRepository.deleteById(id);
  }

  @authenticate.skip()
  @post('/incidents/statistics')
  @response(200, {
    description: 'Incident statistics including high severity percentage, actions closed within due date, and overdue actions',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getIncidentStatistics(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [],
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};

    const incidents = await this.reportIncidentRepository.find({ include: [{ relation: 'locationOne' }, { relation: 'locationThree' }] });

    const dateFormat = 'YYYY-MM-DDTHH:mm:ss';

    // Filter incidents based on the date-time range
    const filteredIncidents = incidents.filter((incident) => {
      const createdDate = moment(incident.created, dateFormat);
      if (!createdDate.isValid()) {
        console.error(`Invalid date format for incident: ${incident.created}`);
        return false;
      }

      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'minute', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'minute');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'minute');
      }
      return true;
    });

    let highSeverityCount = 0;
    const totalIncidents = filteredIncidents.length;

    let actionsClosedOnTime = 0;
    let overdueActionsCount = 0;
    let totalActionsWithDueDates = 0;

    const detailedReport: any[] = [];

    filteredIncidents.forEach((incident) => {
      const actualImpact = incident.actualImpact?.toLowerCase() || '';
      const isHighSeverity =
        actualImpact.includes('level 3') || actualImpact.includes('level 4') || actualImpact.includes('level 5');

      if (isHighSeverity) {
        highSeverityCount += 1;
      }

      const allActions = [
        ...(incident.riskControl?.immediateActions || []),
        ...(incident.riskControl?.controlMeasures || []),
        ...(incident.riskControl?.riskAssessment || []),
      ];

      let actionsClosedOnTimeForIncident = 0;
      let overdueActionsForIncident = 0;
      let totalActionsForIncident = 0;

      allActions.forEach((action) => {
        if (action.completionDate && action.date) {
          const dueDate = moment(action.date, dateFormat); // Action due date as date-time
          const completionDate = moment(action.completionDate, dateFormat);

          if (completionDate.isSameOrBefore(dueDate)) {
            actionsClosedOnTimeForIncident += 1;
            actionsClosedOnTime += 1;
          } else {
            overdueActionsForIncident += 1;
            overdueActionsCount += 1;
          }

          totalActionsForIncident += 1;
          totalActionsWithDueDates += 1;
        }
      });

      detailedReport.push({
        incidentId: incident.maskId,
        incidentTitle: incident.title,
        incidentDescription: incident.description,
        incidentImpactClassification: incident.actualImpact,
        totalActions: totalActionsForIncident,
        actionsClosedOnTime: actionsClosedOnTimeForIncident,
        overdueActions: overdueActionsForIncident,
        highSeverity: isHighSeverity ? 'Yes' : 'No',
      });
    });

    const highSeverityPercentage = totalIncidents > 0 ? ((highSeverityCount / totalIncidents) * 100).toFixed(2) : '0.00';
    const actionsClosedOnTimePercentage =
      totalActionsWithDueDates > 0 ? ((actionsClosedOnTime / totalActionsWithDueDates) * 100).toFixed(2) : '0.00';
    const overdueActionsPercentage =
      totalActionsWithDueDates > 0 ? ((overdueActionsCount / totalActionsWithDueDates) * 100).toFixed(2) : '0.00';

    return {
      totalIncidents,
      highSeverityCount,
      highSeverityPercentage: `${highSeverityPercentage}%`,
      totalActionsWithDueDates,
      actionsClosedOnTime,
      actionsClosedOnTimePercentage: `${actionsClosedOnTimePercentage}%`,
      overdueActionsCount,
      overdueActionsPercentage: `${overdueActionsPercentage}%`,
      detailedReport,
    };
  }



  @authenticate.skip()
  @post('/incidents/analytics')
  @response(200, {
    description: 'Comprehensive incident analytics including statistics, category distribution, impact classification, and more',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getIncidentAnalytics(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [],
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};

    try {
      // Fetch all incidents with relations
      const incidents = await this.reportIncidentRepository.find({
        include: [
          { relation: 'locationOne' },
          { relation: 'locationThree' },
          { relation: 'incidentCircumstanceCategory' },
          { relation: 'incidentCircumstanceType' },
          { relation: 'workActivity' },
          { relation: 'riskCategory' },
        ],
      });

      // Filter incidents based on the date-time range
      const filteredIncidents = this.filterIncidentsByDateRange(incidents, from, to);

      // Build comprehensive analytics response
      const analyticsResponse = {
        // Basic statistics
        statistics: await this.calculateIncidentStatistics(filteredIncidents),

        // Category distribution
        categoryDistribution: await this.calculateCategoryDistribution(filteredIncidents),

        // Impact classification
        impactClassification: await this.calculateImpactClassification(filteredIncidents),

        // Top 5 categories
        top5Categories: await this.calculateTop5Categories(filteredIncidents),

        // Incident circumstances
        incidentCircumstances: await this.calculateIncidentCircumstances(filteredIncidents),

        // Top 5 workplace activities
        top5WorkplaceActivities: await this.calculateTop5WorkplaceActivities(filteredIncidents),

        // Top 5 GMS categories
        top5GMS: await this.calculateTop5GMS(filteredIncidents),
      };

      return analyticsResponse;

    } catch (error) {
      console.error('Error processing incident analytics:', error);
      throw new HttpErrors.InternalServerError('Failed to process incident analytics. Please try again later.');
    }
  }


  @authenticate.skip()
  @get('/incidents/incident-category-distribution')
  @response(200, {
    description: 'Pie chart for incident categories',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getIncidentCategoryDistribution(): Promise<object> {
    const incidents = await this.reportIncidentRepository.find();

    const totalIncidents = incidents.length;
    const categoryCounts: Record<string, number> = {};

    // Group incidents by category and count
    incidents.forEach((incident) => {
      const category = incident.IncidentCategory || 'Unknown';
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });

    // Format for pie chart
    const categoryDistribution = Object.keys(categoryCounts).map((category) => {
      const count = categoryCounts[category];
      const percentage = ((count / totalIncidents) * 100).toFixed(2);
      return {
        category,
        count,
        percentage: `${percentage}%`,
      };
    });

    return {
      title: "Incident Category Distribution",
      totalIncidents,
      categoryDistribution,
    };
  }


  @authenticate.skip()
  @get('/incidents/impact-classification')
  async getIncidentImpactClassification(): Promise<object> {
    const incidents = await this.reportIncidentRepository.find();

    // Get the past 12 months range
    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();

    const impactCountsByMonth: Record<string, Record<string, number>> = {};
    months.forEach((month) => {
      impactCountsByMonth[month] = {
        "Near-Miss": 0,
        "Level 1": 0,
        "Level 2": 0,
        "Level 3": 0,
        "Level 4": 0,
        "Level 5": 0,
      };
    });

    // Filter incidents based on date range
    const filteredIncidents = incidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss'); // Parse string date
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    filteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const impact = incident.actualImpact || 'Unknown';
      if (impactCountsByMonth[monthYear]?.[impact] !== undefined) {
        impactCountsByMonth[monthYear][impact] += 1;
      }
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      ...impactCountsByMonth[monthYear],
    }));

    return {
      title: "Incident Impact Classification",
      data: formattedData,
    };
  }


  @authenticate.skip()
  @get('/incidents/top-5-categories')
  async getTop5Categories(): Promise<object> {
    const incidents = await this.reportIncidentRepository.find();

    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();
    const categoryCountsByMonth: Record<string, Record<string, number>> = {};

    months.forEach((month) => {
      categoryCountsByMonth[month] = {};
    });

    const filteredIncidents = incidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    filteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const category = incident.IncidentCategory || 'Unknown';

      if (!categoryCountsByMonth[monthYear][category]) {
        categoryCountsByMonth[monthYear][category] = 0;
      }
      categoryCountsByMonth[monthYear][category] += 1;
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      categories: Object.entries(categoryCountsByMonth[monthYear])
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([category, count]) => ({ category, count })),
    }));

    return {
      title: "Top 5 Safety Incident Categories",
      data: formattedData,
    };
  }


  @authenticate.skip()
  @get('/incidents/incident-circumstances')
  async getIncidentCircumstances(): Promise<object> {
    // Fetch incidents along with their related categories and types
    const incidents = await this.reportIncidentRepository.find({
      include: [
        { relation: 'incidentCircumstanceCategory' },
        { relation: 'incidentCircumstanceType' }
      ]
    });

    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();

    // Nested record: Month -> Category -> Type -> Count
    const circumstanceCountsByMonth: Record<string, Record<string, Record<string, number>>> = {};

    // Initialize structure for each month
    months.forEach((month) => {
      circumstanceCountsByMonth[month] = {
        'Environment': {},
        'Health': {},
        'Safety': {}
      };
    });

    // Filter incidents within the past 12 months
    const filteredIncidents = incidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    // Count incidents by category and type
    filteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');

      // Get category and type names from related models, defaulting to 'Unknown' if not available
      const category = incident.incidentCircumstanceCategory?.name || 'Unknown Category';
      const type = incident.incidentCircumstanceType?.name || 'Unknown Type';

      // Only count incidents under hardcoded categories: Environment, Health, Safety
      if (['Environment', 'Health', 'Safety'].includes(category)) {
        if (!circumstanceCountsByMonth[monthYear][category][type]) {
          circumstanceCountsByMonth[monthYear][category][type] = 0;
        }
        circumstanceCountsByMonth[monthYear][category][type] += 1;
      }
    });

    // Formatting data for the response
    const formattedData = months.map((monthYear) => ({
      monthYear,
      categories: Object.entries(circumstanceCountsByMonth[monthYear]).map(([category, types]) => ({
        category,
        types: Object.entries(types)
          .sort((a, b) => b[1] - a[1])  // Sort types by count in descending order
          .map(([type, count]) => ({ type, count })),
      })),
    }));

    return {
      title: "Incident Circumstances by Category",
      data: formattedData,
    };
  }


  @authenticate.skip()
  @get('/incidents/top-5-workplace-activities')
  async getTop5WorkplaceActivities(): Promise<object> {
    const incidents = await this.reportIncidentRepository.find({
      include: [

        { relation: 'workActivity' },

      ],
    });

    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();
    const activityCountsByMonth: Record<string, Record<string, number>> = {};

    months.forEach((month) => {
      activityCountsByMonth[month] = {};
    });

    const filteredIncidents = incidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    filteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const activity = incident.workActivity?.name || 'Unknown';

      if (!activityCountsByMonth[monthYear][activity]) {
        activityCountsByMonth[monthYear][activity] = 0;
      }
      activityCountsByMonth[monthYear][activity] += 1;
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      activities: Object.entries(activityCountsByMonth[monthYear])
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([activity, count]) => ({ activity, count })),
    }));

    return {
      title: "Top 5 Workplace Activities",
      data: formattedData,
    };
  }

  @authenticate.skip()
  @get('/incidents/top-5-gms')
  async getTop5GMS(): Promise<object> {
    const incidents = await this.reportIncidentRepository.find({
      include: [

        { relation: 'riskCategory' },

      ],
    });

    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();
    const gmsCountsByMonth: Record<string, Record<string, number>> = {};

    months.forEach((month) => {
      gmsCountsByMonth[month] = {};
    });

    const filteredIncidents = incidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    filteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const gms = incident.riskCategory?.name || 'Unknown';

      if (!gmsCountsByMonth[monthYear][gms]) {
        gmsCountsByMonth[monthYear][gms] = 0;
      }
      gmsCountsByMonth[monthYear][gms] += 1;
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      gmsCategories: Object.entries(gmsCountsByMonth[monthYear])
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([gms, count]) => ({ gms, count })),
    }));

    return {
      title: "Top 5 GMS Categories",
      data: formattedData,
    };
  }

  // Add detailed results endpoint
  @authenticate.skip()
  @post('/incidents/detailed-results')
  @response(200, {
    description: 'Get detailed incident results for specific types',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getIncidentDetailedResults(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
              type: {
                type: 'string',
                enum: ['highSeverity', 'actionsClosedOnTime', 'overdueActions', 'allIncidents'],
                description: 'Type of detailed results to return'
              },
            },
            required: ['type'],
          },
        },
      },
    })
    filterParams: { from?: string; to?: string; type: string }
  ): Promise<object> {
    const { from, to, type } = filterParams;
    const dateFormat = 'YYYY-MM-DDTHH:mm:ss';

    try {
      const incidents = await this.reportIncidentRepository.find({
        include: [
          { relation: 'locationOne' },
          { relation: 'locationThree' },
          { relation: 'incidentCircumstanceCategory' },
          { relation: 'incidentCircumstanceType' },
          { relation: 'workActivity' },
          { relation: 'riskCategory' },
        ],
      });

      // Filter incidents based on the date-time range
      const filteredIncidents = this.filterIncidentsByDateRange(incidents, from, to);

      const highSeverityIncidents: any[] = [];
      const actionsClosedOnTimeIncidents: any[] = [];
      const overdueActionsIncidents: any[] = [];
      const detailedReport: any[] = [];

      filteredIncidents.forEach((incident) => {
        const actualImpact = incident.actualImpact?.toLowerCase() || '';
        const isHighSeverity =
          actualImpact.includes('level 3') || actualImpact.includes('level 4') || actualImpact.includes('level 5');

        const allActions = [
          ...(incident.riskControl?.immediateActions || []),
          ...(incident.riskControl?.controlMeasures || []),
          ...(incident.riskControl?.riskAssessment || []),
        ];

        let actionsClosedOnTimeForIncident = 0;
        let overdueActionsForIncident = 0;
        let totalActionsForIncident = 0;

        allActions.forEach((action) => {
          if (action.completionDate && action.date) {
            const dueDate = moment(action.date, dateFormat);
            const completionDate = moment(action.completionDate, dateFormat);

            if (completionDate.isSameOrBefore(dueDate)) {
              actionsClosedOnTimeForIncident += 1;
            } else {
              overdueActionsForIncident += 1;
            }
            totalActionsForIncident += 1;
          }
        });

        const incidentDetail = {
          incidentId: incident.maskId,
          incidentTitle: incident.title,
          incidentDescription: incident.description,
          incidentImpactClassification: incident.actualImpact,
          totalActions: totalActionsForIncident,
          actionsClosedOnTime: actionsClosedOnTimeForIncident,
          overdueActions: overdueActionsForIncident,
          highSeverity: isHighSeverity ? 'Yes' : 'No',
          fullIncidentData: incident,
        };

        detailedReport.push(incidentDetail);

        if (isHighSeverity) {
          highSeverityIncidents.push(incidentDetail);
        }

        if (actionsClosedOnTimeForIncident > 0) {
          actionsClosedOnTimeIncidents.push(incidentDetail);
        }

        if (overdueActionsForIncident > 0) {
          overdueActionsIncidents.push(incidentDetail);
        }
      });

      const results: any = {
        highSeverity: highSeverityIncidents,
        actionsClosedOnTime: actionsClosedOnTimeIncidents,
        overdueActions: overdueActionsIncidents,
        allIncidents: detailedReport,
      };

      // Return only the requested type
      if (!results[type]) {
        throw new HttpErrors.BadRequest(`Invalid type: ${type}. Valid types are: highSeverity, actionsClosedOnTime, overdueActions, allIncidents`);
      }

      return {
        type,
        count: results[type].length,
        data: results[type],
      };

    } catch (error) {
      console.error('Error getting detailed incident results:', error);
      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }
      throw new HttpErrors.InternalServerError('Failed to get detailed incident results. Please try again later.');
    }
  }

  // Helper methods for analytics calculations
  private filterIncidentsByDateRange(incidents: any[], from?: string, to?: string): any[] {
    const dateFormat = 'YYYY-MM-DDTHH:mm:ss';

    return incidents.filter((incident) => {
      const createdDate = moment(incident.created, dateFormat);
      if (!createdDate.isValid()) {
        console.error(`Invalid date format for incident: ${incident.created}`);
        return false;
      }

      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'minute', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'minute');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'minute');
      }
      return true;
    });
  }

  private async calculateIncidentStatistics(filteredIncidents: any[]): Promise<object> {
    const dateFormat = 'YYYY-MM-DDTHH:mm:ss';
    let highSeverityCount = 0;
    const totalIncidents = filteredIncidents.length;
    let actionsClosedOnTime = 0;
    let overdueActionsCount = 0;
    let totalActionsWithDueDates = 0;

    filteredIncidents.forEach((incident) => {
      const actualImpact = incident.actualImpact?.toLowerCase() || '';
      const isHighSeverity =
        actualImpact.includes('level 3') || actualImpact.includes('level 4') || actualImpact.includes('level 5');

      if (isHighSeverity) {
        highSeverityCount += 1;
      }

      const allActions = [
        ...(incident.riskControl?.immediateActions || []),
        ...(incident.riskControl?.controlMeasures || []),
        ...(incident.riskControl?.riskAssessment || []),
      ];

      allActions.forEach((action) => {
        if (action.completionDate && action.date) {
          const dueDate = moment(action.date, dateFormat);
          const completionDate = moment(action.completionDate, dateFormat);

          if (completionDate.isSameOrBefore(dueDate)) {
            actionsClosedOnTime += 1;
          } else {
            overdueActionsCount += 1;
          }
          totalActionsWithDueDates += 1;
        }
      });
    });

    const highSeverityPercentage = totalIncidents > 0 ? ((highSeverityCount / totalIncidents) * 100).toFixed(2) : '0.00';
    const actionsClosedOnTimePercentage =
      totalActionsWithDueDates > 0 ? ((actionsClosedOnTime / totalActionsWithDueDates) * 100).toFixed(2) : '0.00';
    const overdueActionsPercentage =
      totalActionsWithDueDates > 0 ? ((overdueActionsCount / totalActionsWithDueDates) * 100).toFixed(2) : '0.00';

    return {
      totalIncidents,
      highSeverityCount,
      highSeverityPercentage: `${highSeverityPercentage}%`,
      totalActionsWithDueDates,
      actionsClosedOnTime,
      actionsClosedOnTimePercentage: `${actionsClosedOnTimePercentage}%`,
      overdueActionsCount,
      overdueActionsPercentage: `${overdueActionsPercentage}%`,
    };
  }

  private async calculateCategoryDistribution(filteredIncidents: any[]): Promise<object> {
    const totalIncidents = filteredIncidents.length;
    const categoryCounts: Record<string, number> = {};

    filteredIncidents.forEach((incident) => {
      const category = incident.IncidentCategory || 'Unknown';
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });

    const categoryDistribution = Object.keys(categoryCounts).map((category) => {
      const count = categoryCounts[category];
      const percentage = ((count / totalIncidents) * 100).toFixed(2);
      return {
        category,
        count,
        percentage: `${percentage}%`,
      };
    });

    return {
      title: "Incident Category Distribution",
      totalIncidents,
      categoryDistribution,
    };
  }

  private async calculateImpactClassification(filteredIncidents: any[]): Promise<object> {
    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();

    const impactCountsByMonth: Record<string, Record<string, number>> = {};
    months.forEach((month) => {
      impactCountsByMonth[month] = {
        "Near-Miss": 0,
        "Level 1": 0,
        "Level 2": 0,
        "Level 3": 0,
        "Level 4": 0,
        "Level 5": 0,
      };
    });

    const dateFilteredIncidents = filteredIncidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    dateFilteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const impact = incident.actualImpact || 'Unknown';
      if (impactCountsByMonth[monthYear]?.[impact] !== undefined) {
        impactCountsByMonth[monthYear][impact] += 1;
      }
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      ...impactCountsByMonth[monthYear],
    }));

    return {
      title: "Incident Impact Classification",
      data: formattedData,
    };
  }

  private async calculateTop5Categories(filteredIncidents: any[]): Promise<object> {
    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();
    const categoryCountsByMonth: Record<string, Record<string, number>> = {};

    months.forEach((month) => {
      categoryCountsByMonth[month] = {};
    });

    const dateFilteredIncidents = filteredIncidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    dateFilteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const category = incident.IncidentCategory || 'Unknown';

      if (!categoryCountsByMonth[monthYear][category]) {
        categoryCountsByMonth[monthYear][category] = 0;
      }
      categoryCountsByMonth[monthYear][category] += 1;
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      categories: Object.entries(categoryCountsByMonth[monthYear])
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([category, count]) => ({ category, count })),
    }));

    return {
      title: "Top 5 Safety Incident Categories",
      data: formattedData,
    };
  }

  private async calculateIncidentCircumstances(filteredIncidents: any[]): Promise<object> {
    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();

    const circumstanceCountsByMonth: Record<string, Record<string, Record<string, number>>> = {};

    months.forEach((month) => {
      circumstanceCountsByMonth[month] = {
        'Environment': {},
        'Health': {},
        'Safety': {}
      };
    });

    const dateFilteredIncidents = filteredIncidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    dateFilteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const category = incident.incidentCircumstanceCategory?.name || 'Unknown Category';
      const type = incident.incidentCircumstanceType?.name || 'Unknown Type';

      if (['Environment', 'Health', 'Safety'].includes(category)) {
        if (!circumstanceCountsByMonth[monthYear][category][type]) {
          circumstanceCountsByMonth[monthYear][category][type] = 0;
        }
        circumstanceCountsByMonth[monthYear][category][type] += 1;
      }
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      categories: Object.entries(circumstanceCountsByMonth[monthYear]).map(([category, types]) => ({
        category,
        types: Object.entries(types)
          .sort((a, b) => b[1] - a[1])
          .map(([type, count]) => ({ type, count })),
      })),
    }));

    return {
      title: "Incident Circumstances by Category",
      data: formattedData,
    };
  }

  private async calculateTop5WorkplaceActivities(filteredIncidents: any[]): Promise<object> {
    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();
    const activityCountsByMonth: Record<string, Record<string, number>> = {};

    months.forEach((month) => {
      activityCountsByMonth[month] = {};
    });

    const dateFilteredIncidents = filteredIncidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    dateFilteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const activity = incident.workActivity?.name || 'Unknown';

      if (!activityCountsByMonth[monthYear][activity]) {
        activityCountsByMonth[monthYear][activity] = 0;
      }
      activityCountsByMonth[monthYear][activity] += 1;
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      activities: Object.entries(activityCountsByMonth[monthYear])
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([activity, count]) => ({ activity, count })),
    }));

    return {
      title: "Top 5 Workplace Activities",
      data: formattedData,
    };
  }

  private async calculateTop5GMS(filteredIncidents: any[]): Promise<object> {
    const twelveMonthsAgo = moment().subtract(12, 'months').startOf('month');
    const months = getPast12Months();
    const gmsCountsByMonth: Record<string, Record<string, number>> = {};

    months.forEach((month) => {
      gmsCountsByMonth[month] = {};
    });

    const dateFilteredIncidents = filteredIncidents.filter((incident) => {
      const createdDate = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss');
      return createdDate.isSameOrAfter(twelveMonthsAgo);
    });

    dateFilteredIncidents.forEach((incident) => {
      const monthYear = moment(incident.created, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM');
      const gms = incident.riskCategory?.name || 'Unknown';

      if (!gmsCountsByMonth[monthYear][gms]) {
        gmsCountsByMonth[monthYear][gms] = 0;
      }
      gmsCountsByMonth[monthYear][gms] += 1;
    });

    const formattedData = months.map((monthYear) => ({
      monthYear,
      gmsCategories: Object.entries(gmsCountsByMonth[monthYear])
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([gms, count]) => ({ gms, count })),
    }));

    return {
      title: "Top 5 GMS Categories",
      data: formattedData,
    };
  }
}
